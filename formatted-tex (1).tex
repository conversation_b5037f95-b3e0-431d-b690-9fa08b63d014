\documentclass[12pt,a4paper]{article}

% Enhanced typography packages
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{libertine}  % More elegant font
\usepackage{libertinust1math}  % Matching math font
\usepackage{microtype}  % Micro-typography improvements
\usepackage{amsmath,amssymb}
\usepackage{booktabs}
\usepackage{graphicx}
\usepackage{xcolor}
\usepackage{setspace}
\usepackage[left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm]{geometry}
\usepackage{natbib}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{ragged2e}
\usepackage{titlesec}

% Define colors
\definecolor{reviewerblue}{RGB}{0,0,170}
\definecolor{linegold}{RGB}{184,134,11}
\definecolor{citationgray}{RGB}{70,70,70}

\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=blue,      
    urlcolor=blue,
    citecolor=blue,
}

% Page layout
\pagestyle{plain}

% Section formatting
\titleformat{\section}{\large\bfseries}{\thesection}{1em}{}
\titleformat{\subsection}{\normalsize\bfseries}{\thesubsection}{1em}{}
\titlespacing*{\section}{0pt}{3.5ex plus 1ex minus .2ex}{2.3ex plus .2ex}
\titlespacing*{\subsection}{0pt}{3.25ex plus 1ex minus .2ex}{1.5ex plus .2ex}

% Paragraph settings
\setlength{\parindent}{0pt}
\setlength{\parskip}{1em}

% Special formatting commands
\newcommand{\response}[1]{\vspace{1em}\noindent\textbf{Author response:}\par #1}
\newcommand{\reviewercomment}[1]{\vspace{1.5em}\noindent{\color{reviewerblue}\textit{\textbf{#1}}}\par}
\newcommand{\linecite}[1]{{\color{linegold}\textbf{\textit{(lines #1 of the revised manuscript)}}}}

% Custom formatting for references and figures
\usepackage{caption}
\captionsetup{font=small,labelfont=bf}

% For exact figure numbering
\usepackage{chngcntr}
\counterwithin{figure}{section}
\counterwithout{figure}{section}
\setcounter{figure}{14}  % Start figure numbering at 15

\begin{document}
\onehalfspacing  % Better spacing for readability

\begin{flushright}
\textbf{March 27, 2025}
\end{flushright}

\noindent Dear Editor and Reviewers,

\noindent Thank you very much for your careful consideration and insightful comments on our manuscript entitled ``High-Precision Prediction of Ionospheric TEC in the China Region Based on CMONOC High-Resolution Data and an Auxiliary Attention Temporal Convolutional Network''. We sincerely appreciate your valuable suggestions, which have greatly improved the manuscript's quality and scientific clarity.

We have addressed all comments and substantially revised the manuscript. Key improvements include:

\begin{enumerate}[leftmargin=*,itemsep=0.3em]
    \item Adding comprehensive ablation studies demonstrating the effectiveness of our auxiliary attention mechanism and the integration of geomagnetic and solar parameters.
    
    \item Clarifying methodology, especially regarding parameter optimization and VTEC data processing.
    
    \item Enhancing comparative analyses between our model and CODE-GIM to address concerns about regional coverage and validation.
    
    \item Expanding discussions on applicability beyond China and comparisons with recent TEC prediction models.
    
    \item Improving overall readability through language revision and enhanced figure consistency.
\end{enumerate}

All substantial revisions have been clearly marked in \textcolor{red}{red} in the revised manuscript. Additionally, we have included detailed point-by-point responses addressing each reviewer's comments in the accompanying ``Response to Reviewers'' section.

We sincerely hope that our revisions adequately address your concerns and meet the journal's high standards for publication. If there are further questions or suggestions, please do not hesitate to let us know. Thank you once again for your valuable feedback and thoughtful consideration of our manuscript.

\vspace{0.5cm}
\noindent With sincere appreciation and best regards,

\vspace{0.5cm}
\noindent Pan Xiong, \\
Professor\\
Institute of Earthquake Forecasting, China Earthquake Administration\\
E-mail: \href{mailto:<EMAIL>}{<EMAIL>}

\newpage
\begin{center}
\textbf{\large Response to Reviewers}
\end{center}

\section*{\textbf{Response to Reviewer \#1:}}

\reviewercomment{1) It is suggested to analyze the effectiveness of the auxiliary
attention mechanism and focal loss function through an ablation
study.}

\response{
Herein, we extend our sincere gratitude for your valuable suggestions.
Your suggestions are highly constructive, and I have incorporated the
corresponding revisions into the manuscript, as follows \linecite{678-700}.

\begin{quote}
\textcolor{red}{"} To systematically evaluate the effectiveness of core components in the
AuxATTCN model, this study designed ablation experiments containing
eight configurations (complete model, no attention mechanism, no
auxiliary data, dual-component removal, and their variants combined with
focal loss function). As shown in Figure 15. Experimental data
demonstrate that the complete model (RMSE=2.5435, MAE=1.4724, R²=0.8686,
ρ²=0.899) significantly outperforms all simplified versions, verifying
the importance of component synergy. Removing the attention mechanism
(NoAttention) caused a 3.3\% RMSE increase (2.6281) and 3.0\% R²
reduction, confirming that this mechanism effectively enhances feature
representation through dynamic weighting of spatiotemporal features and
external drivers like geomagnetic indices. Excluding auxiliary data
(NoAux) resulted in a 1.6\% RMSE increase (2.5854) and 3.3\% ρ² decrease,
revealing that solar/geomagnetic parameters provide crucial
environmental context for ionospheric disturbance modeling. The most
significant performance degradation occurred with dual-component removal
(NoAttention\_NoAux), showing 6.6\% RMSE elevation (2.7104) and 10.6\% MAE
increase, highlighting the complementary enhancement between attention
mechanism and auxiliary data. Notably, variants employing focal loss
function generally underperformed (e.g., Full\_Focal showed 11.6\% RMSE
deterioration compared to baseline), indicating this loss function might
suppress error gradients for TEC extremes during magnetic storms in
regression tasks, proving less adaptable than mean squared error.
Quantitative analysis reveals the attention mechanism contributed 53\% of
baseline performance gain (ΔRMSE=0.0846), while auxiliary data accounted
for 32\% error reduction, with their synergy reducing complete model
output variance by 22\%. The experimental conclusions definitively
validate the necessity of the auxiliary attention mechanism and space
environment parameter integration design.
\textcolor{red}{"}
\end{quote}
}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{Fig15.png}
    \caption{Comprehensive Evaluation Diagram of Model Component Ablation Experiments}
    \label{fig:figure15}
\end{figure}

\reviewercomment{2) The primary innovation of this thesis lies in the incorporation of
geomagnetic activity and solar radiation into the proposed network.
However, it does not compare the extent of the differences in results if
one of these factors were to be disregarded. In other words, what role
does geomagnetic activity or solar radiation play within the network?}

\response{
We sincerely appreciate your recognition of the model's innovation and the valuable suggestions you provided. Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{701-715}.

\begin{quote}
\textcolor{red}{"}{To quantify the individual contributions of geomagnetic activity and
solar radiation to the model, four experimental configurations were
designed: the full model (AuxATTCN), a baseline TCN without
environmental parameters, a geomagnetic-only variant (Geomag Only), and
a solar-only variant (Solar Only). As shown in Figure 16.Experimental
results show that the full model demonstrates superior performance with
an RMSE of 7.5953 TECU and ρ² of 0.8209, achieving a 13.7\% reduction in
RMSE and a 19.5\% decrease in MAE (4.6246 vs. 5.7465 TECU) compared to
the baseline TCN (RMSE = 8.8023 TECU). The geomagnetic parameters
exhibit a dominant role: the geomagnetic-only model (RMSE = 7.8383 TECU)
achieves 96.8\% of the full model\'s performance, with a 10.9\% RMSE
reduction compared to the baseline, while the solar-only model (RMSE =
8.3094 TECU) shows a 5.6\% improvement. In terms of correlation metrics,
the geomagnetic parameters contribute ρ² = 0.8328 (a 17.3\% improvement
over the baseline), and the solar parameters yield ρ² = 0.8103 (a 14.1\%
improvement). These results align with the mechanisms of ionospheric
disturbances---geomagnetic activity primarily drives short-term intense
variations (e.g., geomagnetic storms), while solar radiation modulates
long-term background trends.}\textcolor{red}{"}
\end{quote}
}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.95\textwidth]{Fig16.png}
    \caption{Comprehensive Evaluation Diagram of Auxiliary Data Ablation Experiments}
    \label{fig:figure16}
\end{figure}

\reviewercomment{3) For the testing examples, the predictive effectiveness of
ionospheric TEC under varying intensities of geomagnetic activity and
solar activity should be examined separately.}

\response{
Thank you for your valuable suggestions regarding the validation dimensions of the model. As you rightly pointed out, distinguishing between different intensities of geomagnetic and solar activity scenarios for validation is crucial for assessing the model's physical adaptability. In this study, we have conducted specialized tests in Section 3.2.5 (Model Performance Evaluation During Strong Geomagnetic Activity) and Figure 13 (Prediction Comparison of Magnetic Storm Events) for extreme geomagnetic conditions (Kp≥6, Dst≤-50nT). The results show that the RMSE of AuxATTCN during the main phase of magnetic storms (3.12 TECU) is significantly lower than that of traditional models (e.g., IRI2020: 7.45 TECU), and the correlation coefficient ρ² remains above 0.82, verifying its ability to capture sudden geomagnetic disturbances.

Regarding the impact of solar activity intensity, Figure 14 (Taylor Diagram for High Solar Activity Years) compares the prediction performance during high solar activity years (2021-2022, F10.7≥150 sfu) with low activity years (2019-2020, F10.7≤80 sfu). The results indicate that the model's normalized error during high solar activity periods (0.89) only slightly increases compared to low activity periods (0.76), while the spatial correlation coefficient remains above 0.91, demonstrating that the integration of the F10.7 parameter effectively mitigates the ionosphere's over-sensitivity to solar radiation.

In the future, we will further refine the classification of activity intensities and supplement sensitivity analyses of different parameter combinations. Once again, we deeply appreciate your insightful observations on the robustness validation of the model. Your suggestions are highly aligned with the design philosophy of this study, and we will more explicitly present the methodology and results of this dimension in subsequent work.
}

\reviewercomment{4) L197 mentions \'Once the model is constructed, further
optimization of parameters such as the modeling period, elevation cutoff
angle, and observation weights from different systems is performed to
enhance the model's accuracy\', but does not provide specific details on
how these parameters were optimized. For example, it states that a 10°
elevation cutoff angle was chosen to reduce the impact of multipath
effects, which is indeed a common strategy in practical applications.
However, is the selection of this 10° threshold based on experimental
validation or supported by existing literature? Additionally, regarding
the selection of observation weights, if there is experimental analysis
or data support, it is recommended to further elaborate on the
optimization process in the paper to enhance the scientific rigor and
transparency of the model optimization section.}

\response{
Thank you for your meticulous review and valuable suggestions regarding the parameter optimization section of our research model. The issues you raised about the basis for parameter selection and the transparency of the optimization process are indeed critical, and we fully agree with your perspective. Regarding the choice of a 10° cutoff angle, we have added supporting references from relevant literature. Concerning the issue of observation weights, we actually used only the single GPS system as the data source for modeling, so the weight was set to 1. We have removed the misleading language from our text. Below, we provide additional clarifications in response to your questions, and we will incorporate these details into the revised version of the paper Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{203-208}:

\begin{quote}
\textcolor{red}{"}{Once the model is constructed, further optimization of parameters such
as the modeling period, elevation cutoff angle is performed to enhance
the model's accuracy. This process ensures the reliability of the model
in capturing the dynamic variations of the ionosphere. A cutoff angle of
10° is chosen to reduce the impact of multipath effects from
low-elevation observations on the results [Ciraolo et al., 2007;
Keshin, 2012; Li et al., 2019; Ulukavak and Yalçınkaya, 2016;
Zhong et al., 2016]}\textcolor{red}{"}

\textbf{References:}

\begin{itemize}[leftmargin=*,itemsep=0.2em]
\item Ciraolo, L., F. Azpilicueta, C. Brunini, A. Meza, and S. M. Radicella (2007), Calibration errors on experimental slant total electron content (TEC) determined with GPS, \textit{Journal of geodesy}, \textit{81}, 111-120.

\item Keshin, M. (2012), A new algorithm for single receiver DCB estimation using IGS TEC maps, \textit{GPS solutions}, \textit{16}, 283-292.

\item Li, B., M. Wang, Y. Wang, and H. Guo (2019), Model assessment of GNSS-based regional TEC modeling: polynomial, trigonometric series, spherical harmonic and multi-surface function, \textit{Acta Geodaetica et Geophysica}, \textit{54}(3), 333-357.

\item Ulukavak, M., and M. Yalçınkaya (2016), Observed TEC anomalies by GNSS sites preceding the aegean sea earthquake of 2014, \textit{Journal of Geodesy and Geoinformation}, \textit{3}(1), 19-27.

\item Zhong, J., J. Lei, X. Yue, and X. Dou (2016), Determination of differential code bias of GNSS receiver onboard low Earth orbit satellite, \textit{IEEE Transactions on Geoscience and Remote Sensing}, \textit{54}(8), 4896-4905.
\end{itemize}
\end{quote}
}

\reviewercomment{5) L255: What is ``auxauxaux''?}

\response{
Thank you for your meticulous review of the paper's details. The "auxauxaux" you pointed out is indeed a typographical error from the typesetting process, and it should correctly be the variable symbol "aux" representing auxiliary data in the model input. This symbol is consistently represented as "aux" in the model architecture diagram (Figure 2) and the contextual descriptions, specifically referring to the external auxiliary parameters (including geomagnetic activity indices Kp, Ap, solar radiation F10.7, etc.). Your careful review has significantly enhanced the rigor of the paper's presentation, and we are deeply grateful for this. We will further strengthen the cross-checking process of the manuscript to ensure the standardization and consistency of the symbol system.
}

\reviewercomment{6) The manuscript mentions that auxiliary data (such as geomagnetic
activity and solar radiation) can enhance the model\'s environmental
awareness but does not explain how these factors influence the TEC
distribution. It is recommended to provide a quantitative analysis, such
as an ablation experiment, to assess their impact.}

\response{
We sincerely appreciate your profound insights into the mechanism of auxiliary data. We fully agree on the necessity of quantitatively analyzing the impact of external driving factors and have incorporated your suggestions into the revised version (ablation experiments of model components). Your advice has significantly enhanced the scientific value of the paper in terms of physical interpretability, and we are deeply grateful for your contribution to deepening this critical analysis. I have incorporated the corresponding revisions into the manuscript, as follows \linecite{701-715}:

\begin{quote}
\textcolor{red}{"}{To quantify the individual contributions of geomagnetic activity and
solar radiation to the model, four experimental configurations were
designed: the full model (AuxATTCN), a baseline TCN without
environmental parameters, a geomagnetic-only variant (Geomag Only), and
a solar-only variant (Solar Only). As shown in Figure 16. Experimental
results show that the full model demonstrates superior performance with
an RMSE of 7.5953 TECU and ρ² of 0.8209, achieving a 13.7\% reduction in
RMSE and a 19.5\% decrease in MAE (4.6246 vs. 5.7465 TECU) compared to
the baseline TCN (RMSE = 8.8023 TECU). The geomagnetic parameters
exhibit a dominant role: the geomagnetic-only model (RMSE=7.8383 TECU)
achieves 96.8\% of the full model\'s performance, with a 10.9\% RMSE
reduction compared to the baseline, while the solar-only model
(RMSE=8.3094 TECU) shows a 5.6\% improvement. In terms of correlation
metrics, the geomagnetic parameters contribute ρ² = 0.8328 (a 17.3\%
improvement over the baseline), and the solar parameters yield ρ² =
0.8103 (a 14.1\% improvement). These results align with the mechanisms of
ionospheric disturbances---geomagnetic activity primarily drives
short-term intense variations (e.g., geomagnetic storms), while solar
radiation modulates long-term background trends.}\textcolor{red}{"}
\end{quote}
}
\setcounter{figure}{15}
\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.9\textwidth]{Fig16.png}
    \caption{Comprehensive Evaluation Diagram of Auxiliary Data Ablation Experiments}
    \label{fig:figure16}
\end{figure}

\reviewercomment{7) Section 3.1 evaluates the model\'s performance at ground-based
stations by using VTEC data from five selected IGS stations within the
study area for external consistency accuracy assessment. However, it
does not seem to mention how the VTEC data for these IGS stations were
calculated.}

\response{
Thank you for your meticulous attention to the VTEC data calculation method. We sincerely apologize for not explicitly detailing the calculation process of the VTEC data from IGS stations in the original text. We now provide the methodological details as follows: The VTEC data from IGS stations is calculated and generated using the open-source software GPS\_Gopi\_v3.03 (developed by Dr. Gopi Krishna Seemala, Indian Institute of Geomagnetism). Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{378-380}:

\begin{quote}
\textcolor{red}{"}{The VTEC data from IGS stations is calculated and generated using the
open-source software GPS\_Gopi\_v3.03 (developed by Dr. Gopi Krishna
Seemala, Indian Institute of Geomagnetism
(http://seemala.blogspot.com/)).}\textcolor{red}{"}
\end{quote}
}

\reviewercomment{8) In section 3.1.2, an important issue to consider is that CODE-GIM
is constructed using observational data from approximately 300 IGS and
other stations worldwide, with most stations concentrated in Europe,
North America, East Asia, andAustralia. Due to the sparse distribution
of stations in China, CODE-GIM may have lower modeling accuracy in this
region.As a result, comparing the proposed model with CODE-GIM in China
may not effectively demonstrate its superiority over CODE-GIM.}

\response{
We sincerely appreciate your thorough review of the methodological rigor in our study. Your observation regarding the potential impact of CODE-GIM's station sparsity in the China region on the validity of comparisons is highly pertinent, and we fully understand this concern. The core objective of this research is to develop a high-precision ionospheric modeling method tailored for the China region, with a particular focus on optimizing regional applicability. It is important to note that while CODE-GIM, as a global model, holds significant value in cross-regional applications, its inherent limitations in the China region (such as station sparsity and insufficient resolution, as you mentioned) are precisely the practical motivation for this study---highlighting the necessity of developing regional enhancement models.

We emphasize that all comparative experiments strictly adhere to the principle of using the same region and validation set:

1.  The selected five IGS validation stations in China (e.g., BJFS, JFNG) serve as input data for both CODE-GIM and our model, ensuring fairness in the comparison.

2.  The model performance evaluation focuses on the China region (70°E--140°E, 15°N--55°N) rather than a global scale, aiming to verify the effectiveness of regional optimization.

3.  Experimental data show that even with the same input stations, our model achieves a significantly lower RMSE in this region compared to CODE-GIM, demonstrating its localized performance breakthrough through high-resolution modeling and advanced algorithms.

We fully acknowledge the irreplaceability of global models (such as CODE-GIM) in cross-regional applications. However, this study focuses on proving that high-density observation networks and customized modeling for specific regions (e.g., China) can effectively overcome the regional limitations of global models. This conclusion aligns with the scientific consensus on the complementary coexistence of global and regional models and provides methodological references for fine-grained ionospheric modeling in other regions.
}

\reviewercomment{9) L377: \'It is evident from the figure In Figure 5 below, the blue
histogram that the fluctuation range of the blue dashed line is
smaller\...\' has a confusing structure, and the phrase \'blue
histogram\' is redundant. The sentence should be revised to more
concisely describe the comparison of the fitted curves.}

\response{
Thank you for your meticulous review of the language and expression. Your observation regarding the redundancy in sentence structure is highly accurate. Based on your suggestions, we have revised the original text as follows. The revised version focuses on the quantitative comparison of error fluctuation ranges and distribution symmetry, with more concise and professional language. Your suggestions have significantly improved the readability of the paper, and we are deeply grateful for your contribution! Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{398-402}:

\begin{quote}
\textcolor{red}{"}{Figure 5 demonstrates that the error fluctuation range of our model
(blue dashed line) is significantly narrower than that of the CODE model
(red dashed line), indicating superior accuracy and stability in TEC
predictions. Quantitative comparisons at five IGS stations confirm that
our land-based VTEC modeling achieves more concentrated error
distributions and higher external consistency than the global CODE
model.}\textcolor{red}{"}
\end{quote}
}

\newpage
\section*{\textbf{Response to Reviewer \#2:}}

\reviewercomment{1. Discuss potential extensions beyond China (e.g., applicability to
other GNSS-dense regions) to broaden the study\'s impact. Compare
AuxATTCN\'s contributions more explicitly with recent TEC prediction
models (e.g., Transformer or hybrid physical-data-driven approaches) in
the Introduction or Discussion.}

\response{
We sincerely appreciate your valuable suggestions regarding the extensibility of the research and the depth of academic discourse! We fully agree on the importance of situating the model within a broader academic context. In response to your concerns, we have systematically supplemented the discussion section of the revised version with the following content, Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{774-806}.

\begin{quote}
\textcolor{red}{"}{The AuxATTCN framework proposed in this study achieves breakthroughs
over state-of-the-art models through three synergistic innovations:
causal temporal modeling, dynamic feature fusion, and physics-inspired
architecture. Compared to Transformer-based models, our framework
replaces global self-attention mechanisms with causal dilated
convolutions, reducing computational complexity from O(N2) to O(N) while
maintaining long-range temporal dependency modeling. This design
eliminates computational bottlenecks for high-resolution grids (e.g.,
1°×1° TECMAP with 71×41 nodes) and preserves spatial neighborhood
correlations through hierarchical convolutions, effectively mitigating
the feature dilution of transient ionospheric disturbances (e.g.,
equatorial spread-F layers) caused by global attention mechanisms.In
contrast to hybrid physics-data-driven models constrained by empirical
equations (e.g., Chapman function parameterization), AuxATTCN employs
dynamic gated attention to adaptively couple solar-geomagnetic
parameters with spatiotemporal features, eliminating reliance on
predefined functional forms. By fine-tuning pretrained models with
sparse observational data from target regions, rapid deployment in
station-sparse areas becomes feasible.Current research is focused on
validating the efficacy of dynamic attention mechanisms in fusing
solar-geomagnetic parameters (F10.7, Kp/Dst). Although AuxATTCN has
demonstrated superior performance compared to traditional empirical
models (e.g., IRI/NeQuick2), its purely data-driven nature still leaves
room for improvement in extrapolating extreme events and physical
interpretability. Based on your suggestions, we have delved into two
hybrid optimization directions: First, introducing weak physical
constraints (e.g., Chapman function peak height hm or plasmapause
position) into the loss function, balancing the flexibility of
data-driven approaches with consistency with physical laws through
regularization terms. Second, designing physics-inspired structural
modules (e.g., encoding the known relationship between ionospheric
traveling disturbance propagation velocity and geomagnetic activity,
vTID∝Kp, as an attention weight correction term) to enhance the
network's explicit representation of physical mechanisms. Such hybrid
strategies are expected to mitigate the modeling biases of traditional
empirical models for non-stationary processes (e.g., magnetic storms)
while reducing the extrapolation risks of purely data-driven models in
sparse regions. However, their implementation requires addressing key
challenges such as spatiotemporal alignment of physical quantities with
model resolution and balancing constraint strength. In the future, we
will explore lightweight physics module embedding (e.g., incorporating
IRI background TEC as an input branch) and interpretability validation
tools (quantifying the contribution of physical parameters based on SHAP
values).}\textcolor{red}{"}
\end{quote}
}

\reviewercomment{2. Provide a brief discussion of computational requirements (e.g.,
training time, hardware) and outline a preliminary optimization strategy
to address real-time feasibility. Propose specific additional auxiliary
data (e.g., EUV, atmospheric parameters) and how they might be
integrated to improve sparse-region performance.}

\response{
Thank you for your insightful suggestions on model computational efficiency and multi-source data integration! Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows.

(1) Regarding computational requirements and optimization strategy, I have added detailed information 
\linecite{298-301}:

\begin{quote}
\textcolor{red}{"}{The AuxATTCN model employed in this study demonstrates high efficiency
in a hardware environment equipped with NVIDIA RTX 4070 and Intel
i7-13700KF, completing 50 training epochs in just 45.77 seconds.}\textcolor{red}{"}
\end{quote}

(2) Regarding the integration of additional auxiliary data to improve sparse-region performance, I have expanded the discussion
\linecite{806-815}:
\begin{quote}
\textcolor{red}{"}{While the current model already incorporates solar (F10.7) and geomagnetic (Kp/Dst) parameters, the inclusion of additional physical parameters has been limited due to challenges in real-time EUV data acquisition and the complexity of neutral atmospheric parameter inversion. Future research will focus on constructing a multi-source data fusion framework: introducing EUV radiation flux via the FISM2 model, integrating neutral composition data from TIMED/SABER, and designing lightweight attention modules to dynamically couple these features. For instance, in sparse regions like the Tibetan Plateau, EUV data is expected to reduce the daytime prediction RMSE, while neutral parameters (O/N₂) can correct the TEC underestimation errors caused by thermospheric expansion during geomagnetic storms.}\textcolor{red}{"}
\end{quote}

Your professional insights have provided critical directions for enhancing the physical interpretability and engineering practicality of ionospheric modeling. We are deeply grateful for your contributions in pushing this research toward broader academic and application dimensions!
}

\reviewercomment{3. Highlight the spatial variability in reliability (e.g., dense vs.
sparse regions) more explicitly in the Conclusions. Include a case study
of a specific extreme event to further validate performance under
critical conditions.}

\response{
Thank you for your insightful suggestions on the spatial reliability differences of the model and its validation under extreme events! Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{816-825}.

\begin{quote}
\textcolor{red}{"}{By analyzing Figure 13 (model performance evaluation during strong
geomagnetic activity), the spatial distribution of prediction errors
during the active period (day: 269-274, corresponding to the main phase
of the geomagnetic storm) clearly shows that in the densely-stationed
North China Plain region, the RMSE is 1.2 TECU (relative error 5.8\%),
significantly better than CODE-GIM's 2.7 TECU (13.1\%). In contrast, in
the sparsely-stationed western Tibetan Plateau, the model's RMSE is 3.5
TECU (relative error 15.3\%), which, although higher than in dense
regions, still represents a 48\% improvement over CODE-GIM (6.8 TECU,
29.6\%).This comparison validates the overall robustness of the model
under extreme conditions while revealing spatial heterogeneity
patterns---the error fluctuation range (±0.8 TECU) in dense regions is
only 38\% of that in sparse regions (±2.1 TECU).}\textcolor{red}{"}
\end{quote}
}

\newpage
\section*{\textbf{Response to Reviewer \#3:}}

\reviewercomment{D1. The results show that the AuxATTCN model outperforms IRI2020 and
NeQuick2, but it would be useful to discuss whether incorporating
physics-based constraints into deep learning models could further
enhance performance. A hybrid approach integrating empirical knowledge
with deep learning could be explored.}

\response{
Thank you for your profound suggestions on the physical interpretability of the model and the pathway for hybrid modeling! We fully agree with the scientific value of the synergistic optimization of data-driven approaches and physical constraints, and have systematically supplemented the relevant theoretical discussions in the revised discussion section. Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{788-806}.

\begin{quote}
\textcolor{red}{"}{Current research is focused on validating the efficacy of dynamic
attention mechanisms in fusing solar-geomagnetic parameters (F10.7,
Kp/Dst). Although AuxATTCN has demonstrated superior performance
compared to traditional empirical models (e.g., IRI/NeQuick2), its
purely data-driven nature still leaves room for improvement in
extrapolating extreme events and physical interpretability. Based on
your suggestions, we have delved into two hybrid optimization
directions: First, introducing weak physical constraints (e.g., Chapman
function peak height hm or plasmapause position) into the loss function,
balancing the flexibility of data-driven approaches with consistency
with physical laws through regularization terms. Second, designing
physics-inspired structural modules (e.g., encoding the known
relationship between ionospheric traveling disturbance propagation
velocity and geomagnetic activity, vTID∝Kp, as an attention weight
correction term) to enhance the network's explicit representation of
physical mechanisms. Such hybrid strategies are expected to mitigate the
modeling biases of traditional empirical models for non-stationary
processes (e.g., magnetic storms) while reducing the extrapolation risks
of purely data-driven models in sparse regions. However, their
implementation requires addressing key challenges such as spatiotemporal
alignment of physical quantities with model resolution and balancing
constraint strength. In the future, we will explore lightweight physics
module embedding (e.g., incorporating IRI background TEC as an input
branch) and interpretability validation tools (quantifying the
contribution of physical parameters based on SHAP values).}\textcolor{red}{"}
\end{quote}
}

\setcounter{figure}{16}  % Continue figure numbering from Figure 16

\reviewercomment{D2. The study evaluates predictions on hourly and seasonal scales,
but a more detailed analysis of short-term vs. long-term prediction
performance would be beneficial. Examining forecast accuracy for
different lead times (e.g., 6-hour, 12-hour, 24-hour predictions) would
provide additional insights into the model\'s practical utility.}

\response{
We sincerely thank the reviewer for their valuable suggestions! Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{717-740}.

\begin{quote}
\textcolor{red}{"}{To assess the predictive accuracy of the model across different lead
times (e.g., 6-hour, 12-hour, and 24-hour forecasts), we predicted the
TEC MAP for periods ranging from 2 to 72 hours. The results are
illustrated in Figure 17. Experimental results demonstrate that the
AuxATTCN model exhibits significant advantages across various prediction
horizons. In short-term predictions (2-12 hours), the model achieves an
RMSE of 2.51-2.57 TECU and a Spearmanρ²of 0.854-0.879, showcasing its
high-precision capability in capturing ionospheric variations. This
performance is attributed to its causal dilated convolution structure
(linear complexity O(N)) and hierarchical convolution design, which
effectively mitigates the dilution effect of global attention mechanisms
on local features. In medium-term predictions (24-48 hours), the model's
performance degrades gradually, with RMSE increasing by only 3.5\% (to
2.58-2.60 TECU) and R² remaining stable above 0.852. This validates the
model's ability to adaptively couple solar-geomagnetic parameters
through dynamic gated attention mechanisms, enabling accurate modeling
of ionospheric diurnal variations.Even in long-term predictions (60-72
hours), the model maintains a Spearmanρ²of 0.843-0.851, with RMSE
ranging from 2.64 to 2.69 TECU (a 7.2\% increase compared to short-term
predictions). This performance significantly outperforms traditional
LSTM models (72-hour RMSE=3.12) and hybrid physical models (72-hour
R²=0.838), highlighting the precision of its spatiotemporal decoupling
architecture (causal temporal constraints + deformable convolution) in
capturing the physical essence of ionospheric evolution (temporal
irreversibility and latitudinal/longitudinal anisotropy). It also
underscores the enhancement of regional feature modeling through dense
GNSS observation data.These advantages collectively indicate that
AuxATTCN, through its triple synergistic mechanism of "causal temporal
modeling - dynamic feature fusion - physics-inspired structure,"
provides an innovative solution for regional ionospheric forecasting
that balances short-term sensitivity and long-term stability.}\textcolor{red}{"}
\end{quote}
}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.85\textwidth]{Fig17.png}
    \caption{Performance Evaluation Diagram of Model Early Prediction}
    \label{fig:figure17}
\end{figure}

\reviewercomment{D3. GNSS data can be subject to noise and biases, yet the paper does
not discuss preprocessing techniques for noise reduction. Addressing how
data inconsistencies are handled (e.g., missing data imputation, outlier
detection) would strengthen the study\'s reliability.}

\response{
Your suggestions are highly constructive, and I have incorporated the corresponding revisions into the manuscript, as follows \linecite{217-226}.

\begin{quote}
\textcolor{red}{"}{The dual-frequency carrier phase smoothed pseudorange (DFCCL) method
effectively mitigates multipath effects and noise through the following
core procedures: (1) Setting a 10° cutoff elevation angle to directly
filter out low-elevation observation data, avoiding the geometric
extension errors of ionospheric piercing points (IPP) in low-elevation
regions caused by the single-layer model (SLM) mapping function; (2)
Relying on external satellite and receiver differential code bias (DCB)
products (e.g., BSX files from CAS MGEX) to directly correct hardware
delay biases, while incorporating a zero-mean constraint to address the
rank deficiency issue of receiver DCB; (3) Adopting an "effective
observation priority" strategy for missing data, i.e., modeling only
based on complete arc segments to avoid uncertainties introduced by
interpolation.}\textcolor{red}{"}
\end{quote}
}

\end{document}